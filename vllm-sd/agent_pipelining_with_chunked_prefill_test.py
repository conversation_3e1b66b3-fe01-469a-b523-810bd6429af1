#!/usr/bin/env python3
"""
Integration Test for Agent Pipelining with Partial Chunked Prefill.

This test simulates a common multi-agent scenario:
- Agent1 (Request 1) generates a response stream.
- Agent2 (Request 2) uses Agent1's output as part of its prompt, along with its
  own system prompt.

The test validates that vLLM's partial prefill feature can effectively
pipeline these two requests, allowing Agent2's prefill to happen concurrently
with Agent1's generation, thus minimizing the final "time-to-first-token" for
Agent2.

Requirements:
- pytest: pip install pytest
- pytest-asyncio: pip install pytest-asyncio
- Set VLLM_USE_V1=1 environment variable

To Run:
- VLLM_USE_V1=1 pytest -s -v test_agent_pipelining_with_chunked_prefill.py
"""

import asyncio
import os
import time
import uuid
from typing import AsyncGenerator

import pytest
import pytest_asyncio

# 确保使用 v1 引擎
os.environ["VLLM_USE_V1"] = "1"

from vllm import AsyncLLM, SamplingParams
from vllm.config import (VllmConfig, ModelConfig, CacheConfig, ParallelConfig, 
                         SchedulerConfig, DeviceConfig, LoadConfig, 
                         DecodingConfig, ObservabilityConfig)

# --- 测试配置 ---
MODEL_NAME = "facebook/opt-125m"
CHUNK_SIZE_CHARS = 30  # 客户端缓冲区的分块大小（以字符为单位）

# --- 引擎 Fixture ---

def create_test_config():
    """为测试创建一个最小化的VllmConfig。"""
    model_config = ModelConfig(
        model=MODEL_NAME, tokenizer=MODEL_NAME, tokenizer_mode='auto',
        trust_remote_code=False, dtype='auto', seed=0)
    cache_config = CacheConfig(block_size=16, gpu_memory_utilization=0.9, 
                               swap_space=4, cache_dtype='auto')
    parallel_config = ParallelConfig(pipeline_parallel_size=1, 
                                     tensor_parallel_size=1, data_parallel_size=1)
    scheduler_config = SchedulerConfig(max_num_seqs=256, max_num_batched_tokens=4096)
    # 使用CPU进行测试，避免需要GPU环境
    device_config = DeviceConfig(device='cpu') 
    return VllmConfig(
        model_config=model_config, cache_config=cache_config,
        parallel_config=parallel_config, scheduler_config=scheduler_config,
        device_config=device_config, load_config=LoadConfig(), lora_config=None,
        speculative_config=None, decoding_config=DecodingConfig(),
        observability_config=ObservabilityConfig(), prompt_adapter_config=None)

@pytest_asyncio.fixture(scope="module")
async def engine():
    """为所有测试创建并拆卸AsyncLLM引擎的Pytest fixture。"""
    vllm_config = create_test_config()
    # 注意：在CPU上运行，测试的绝对时间会较长，但相对时间的对比仍然有效
    llm_engine = AsyncLLM.from_vllm_config(vllm_config)
    yield llm_engine

# --- 测试核心逻辑 ---

@pytest.mark.asyncio
async def test_agent_pipelining_scenario(engine: AsyncLLM):
    """
    测试核心场景：Agent2 使用 Agent1 的流式输出作为输入。
    """
    print("\n--- [Test Case] Agent Pipelining with Partial Chunked Prefill ---")
    
    # 模拟 Agent1 的任务
    agent1_prompt = "Write a short, creative story about a robot who discovers music."
    agent1_sampling_params = SamplingParams(max_tokens=100, temperature=0.8, top_p=0.95)
    agent1_request_id = f"agent1-{uuid.uuid4()}"

    # 模拟 Agent2 的任务
    agent2_system_prompt = "You are a literary critic. Given the following story, provide a brief analysis of its themes:\n\nSTORY:\n"
    agent2_sampling_params = SamplingParams(max_tokens=50, temperature=0.5)
    agent2_request_id = f"agent2-{uuid.uuid4()}"

    # --- 流水线执行 ---

    # Agent2 的最终输出结果
    agent2_final_output = ""
    
    # 用于测量关键时间点的变量
    t_start = 0
    t_agent1_finished = 0
    t_agent2_finalized = 0
    t_agent2_first_token = 0
    t_agent2_finished = 0

    async def run_agent1_and_feed_agent2(
        agent1_output_stream: AsyncGenerator, 
        agent2_req_id: str
    ):
        """
        这个任务消费Agent1的输出，并将其分块喂给Agent2。
        """
        nonlocal t_agent1_finished, t_agent2_finalized
        
        chunk_buffer = ""
        agent1_full_text = ""
        
        print(f"  (Time: {time.perf_counter() - t_start:.4f}s) Agent1 stream consumer started.")
        
        async for request_output in agent1_output_stream:
            new_text = request_output.outputs[0].text
            agent1_full_text += new_text
            chunk_buffer += new_text

            if len(chunk_buffer) >= CHUNK_SIZE_CHARS:
                print(f"  (Time: {time.perf_counter() - t_start:.4f}s) Chunk threshold reached. Sending chunk to Agent2: '{chunk_buffer.strip()}'")
                await engine.append_prompt_chunk(agent2_req_id, chunk_buffer)
                chunk_buffer = ""
        
        # 发送最后一个不满chunk size的数据块
        if chunk_buffer:
            print(f"  (Time: {time.perf_counter() - t_start:.4f}s) Sending final chunk to Agent2: '{chunk_buffer.strip()}'")
            await engine.append_prompt_chunk(agent2_req_id, chunk_buffer)
            
        t_agent1_finished = time.perf_counter()
        print(f"  (Time: {t_agent1_finished - t_start:.4f}s) Agent1 finished generation. Full text length: {len(agent1_full_text)}")

        # Agent1结束后，Finalize Agent2
        await engine.finalize_prompt(agent2_req_id)
        t_agent2_finalized = time.perf_counter()
        print(f"  (Time: {t_agent2_finalized - t_start:.4f}s) Agent2 prompt finalized.")

    async def run_agent2_consumer(agent2_output_stream: AsyncGenerator):
        """
        这个任务消费Agent2的最终解码输出。
        """
        nonlocal agent2_final_output, t_agent2_first_token, t_agent2_finished
        
        output_list = []
        first_token_time_recorded = False
        
        async for request_output in agent2_output_stream:
            if not first_token_time_recorded and request_output.outputs[0].text.strip():
                t_agent2_first_token = time.perf_counter()
                first_token_time_recorded = True
            output_list.append(request_output.outputs[0].text)
            
        agent2_final_output = "".join(output_list)
        t_agent2_finished = time.perf_counter()
        
    # --- 启动测试 ---

    # 1. 立即为Agent2初始化部分请求
    agent2_generator = engine.generate(
        prompt=agent2_system_prompt,
        sampling_params=agent2_sampling_params,
        request_id=agent2_request_id,
        partial=True,
    )
    print(f"  (Time: 0.0000s) Agent2 partial request initialized.")
    
    # 2. 立即开始Agent1的生成
    agent1_generator = engine.generate(
        prompt=agent1_prompt,
        sampling_params=agent1_sampling_params,
        request_id=agent1_request_id,
    )
    print(f"  (Time: 0.0000s) Agent1 request started.")
    
    # 记录统一的开始时间
    t_start = time.perf_counter()

    # 3. 并发运行所有任务
    await asyncio.gather(
        run_agent1_and_feed_agent2(agent1_generator, agent2_request_id),
        run_agent2_consumer(agent2_generator)
    )

    # --- 结果分析与验证 ---
    print("\n--- [Results & Analysis] ---")
    
    # 验证输出
    assert len(agent2_final_output) > 0, "Agent2 should have produced output."
    print(f"Agent2 Final Output: '{agent2_final_output.strip()}'")
    
    # 打印时间线
    total_duration = t_agent2_finished - t_start
    agent1_duration = t_agent1_finished - t_start
    agent2_wait_and_gen_duration = t_agent2_finished - t_agent1_finished
    ttft_after_finalize = t_agent2_first_token - t_agent2_finalized

    print("\n--- Timeline ---")
    print(f"Total pipeline duration:         {total_duration:.4f}s")
    print(f"Agent1 generation duration:      {agent1_duration:.4f}s")
    print(f"Agent2 wait & gen after A1 done: {agent2_wait_and_gen_duration:.4f}s")
    print("-" * 20)
    print(f"** Time to First Token (TTFT) for Agent2 after prompt was finalized: {ttft_after_finalize:.4f}s **")

    # 核心断言
    # TTFT 应该非常小，因为prefill已经被分摊掉了。
    # 在CPU上这个值可能仍在百毫秒级，但在GPU上应该在几十毫秒内。
    assert ttft_after_finalize < 0.5, "TTFT after finalization should be very low due to pipelined prefill."
    
    print("\n** Analysis **")
    print("The key metric is 'Time to First Token after Finalization'.")
    print("A small value demonstrates that Agent2's prompt prefill was successfully")
    print("overlapped with Agent1's token generation, hiding most of the prefill latency.")
    print("This validates the effectiveness of the partial chunked prefill feature.")
    print("\n✓ Test Agent Pipelining Scenario PASSED")

if __name__ == "__main__":
    # 使用 pytest 运行此脚本
    pytest.main(["-s", "-v", __file__])