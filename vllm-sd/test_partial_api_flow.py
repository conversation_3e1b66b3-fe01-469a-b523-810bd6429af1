#!/usr/bin/env python3
"""
Test script for the partial request API flow.

This script tests the complete flow:
1. Create a partial request with generate()
2. Add chunks with append_prompt_chunk()
3. Finalize the request with finalize_prompt()
"""

import asyncio
import time
from vllm.v1.engine.async_llm import AsyncLL<PERSON>
from vllm.v1.engine.processor import Processor
from vllm.v1.engine.core import EngineCore
from vllm.v1.request import Request
from vllm.sampling_params import SamplingParams
from vllm.config import VllmConfig, ModelConfig, CacheConfig, ParallelConfig, SchedulerConfig, DeviceConfig, LoadConfig, DecodingConfig, ObservabilityConfig


def create_test_config():
    """Create a minimal VllmConfig for testing."""
    model_config = ModelConfig(
        model='facebook/opt-125m',
        tokenizer='facebook/opt-125m',
        tokenizer_mode='auto',
        trust_remote_code=False,
        dtype='auto',
        seed=0,
    )
    
    cache_config = CacheConfig(
        block_size=16,
        gpu_memory_utilization=0.9,
        swap_space=4,
        cache_dtype='auto',
    )
    
    parallel_config = ParallelConfig(
        pipeline_parallel_size=1,
        tensor_parallel_size=1,
        worker_use_ray=False,
        data_parallel_size=1,
    )
    
    scheduler_config = SchedulerConfig(
        max_num_seqs=256,
        use_v2_block_manager=False,
        preemption_mode='swap',
    )
    
    device_config = DeviceConfig(device='cpu')
    load_config = LoadConfig()
    decoding_config = DecodingConfig()
    observability_config = ObservabilityConfig()
    
    return VllmConfig(
        model_config=model_config,
        cache_config=cache_config,
        parallel_config=parallel_config,
        scheduler_config=scheduler_config,
        device_config=device_config,
        load_config=load_config,
        lora_config=None,
        speculative_config=None,
        decoding_config=decoding_config,
        observability_config=observability_config,
        prompt_adapter_config=None,
    )


def test_processor_components():
    """Test individual processor components."""
    print("=== Testing Processor Components ===")
    
    try:
        vllm_config = create_test_config()
        processor = Processor(vllm_config)
        print("✓ Processor created successfully")
        
        # Test process_partial_inputs
        chunk_tokens = processor.process_partial_inputs(prompt="Hello")
        print(f"✓ process_partial_inputs works: {chunk_tokens}")
        
        return True
    except Exception as e:
        print(f"✗ Processor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_request_components():
    """Test Request class components."""
    print("\n=== Testing Request Components ===")
    
    try:
        # Test creating a partial request
        request = Request(
            request_id='test_partial',
            prompt_token_ids=[1, 2, 3],
            multi_modal_inputs=None,
            multi_modal_hashes=None,
            multi_modal_placeholders=None,
            sampling_params=SamplingParams(),
            eos_token_id=None,
            is_prompt_finalized=False,  # This is a partial request
        )
        print(f"✓ Partial Request created: is_prompt_finalized={request.is_prompt_finalized}")
        
        # Test append_prompt_token_ids
        original_length = len(request.prompt_token_ids)
        request.append_prompt_token_ids([4, 5, 6])
        new_length = len(request.prompt_token_ids)
        print(f"✓ append_prompt_token_ids works: {original_length} -> {new_length} tokens")
        
        # Test finalize_prompt
        request.finalize_prompt()
        print(f"✓ finalize_prompt works: is_prompt_finalized={request.is_prompt_finalized}")
        
        return True
    except Exception as e:
        print(f"✗ Request test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_engine_core_components():
    """Test EngineCore components."""
    print("\n=== Testing EngineCore Components ===")
    
    try:
        # Test that the methods exist and have correct signatures
        from vllm.v1.engine.core import EngineCore
        
        # Check if methods exist
        assert hasattr(EngineCore, 'append_prompt_chunk'), "append_prompt_chunk method missing"
        assert hasattr(EngineCore, 'finalize_prompt'), "finalize_prompt method missing"
        
        print("✓ EngineCore methods exist")
        
        # Check scheduler interface
        from vllm.v1.core.sched.scheduler import Scheduler
        assert hasattr(Scheduler, 'get_request'), "get_request method missing"
        
        print("✓ Scheduler methods exist")
        
        return True
    except Exception as e:
        print(f"✗ EngineCore test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_async_llm_components():
    """Test AsyncLLM components."""
    print("\n=== Testing AsyncLLM Components ===")
    
    try:
        # Check if methods exist
        from vllm.v1.engine.async_llm import AsyncLLM
        
        assert hasattr(AsyncLLM, 'generate'), "generate method missing"
        assert hasattr(AsyncLLM, 'append_prompt_chunk'), "append_prompt_chunk method missing"
        assert hasattr(AsyncLLM, 'finalize_prompt'), "finalize_prompt method missing"
        
        print("✓ AsyncLLM methods exist")
        
        # Check method signatures
        import inspect
        
        generate_sig = inspect.signature(AsyncLLM.generate)
        assert 'partial' in generate_sig.parameters, "generate method missing partial parameter"
        print("✓ generate method has partial parameter")
        
        return True
    except Exception as e:
        print(f"✗ AsyncLLM test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("Testing Partial Request API Flow")
    print("=" * 50)
    
    tests = [
        test_processor_components,
        test_request_components,
        test_engine_core_components,
    ]
    
    async_tests = [
        test_async_llm_components,
    ]
    
    # Run synchronous tests
    results = []
    for test in tests:
        results.append(test())
    
    # Run asynchronous tests
    async def run_async_tests():
        async_results = []
        for test in async_tests:
            async_results.append(await test())
        return async_results
    
    async_results = asyncio.run(run_async_tests())
    results.extend(async_results)
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    passed = sum(results)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The API flow should work correctly.")
        print("\nYou can now use the API like this:")
        print("1. async for output in llm.generate(prompt='Hello', partial=True): ...")
        print("2. await llm.append_prompt_chunk(request_id, ' world')")
        print("3. await llm.finalize_prompt(request_id)")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
